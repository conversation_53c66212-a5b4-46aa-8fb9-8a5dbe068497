<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ImageViewer from './components/ImageViewer.vue'
import ImageToolbar from './components/ImageToolbar.vue'
import ImageInfo from './components/ImageInfo.vue'
import type { ImageInfo as ImageInfoType, ToolbarAction, LoadingState } from './types'

defineOptions({
  name: 'UltrasoundImageViewer'
})

// 组件状态
const imageViewerRef = ref<InstanceType<typeof ImageViewer>>()
const currentImageInfo = ref<ImageInfoType | null>(null)
const loadingState = ref<LoadingState>('idle')
const currentZoom = ref(1)

// 导入本地超声图像
import ultrasoundImage1 from '@/common/assets/images/transoud/9445185a9f1f6f50b44220c9fe8796e5.jpg'
import ultrasoundImage2 from '@/common/assets/images/transoud/28f8818f1f81789bc05cae92bf2693a4.jpg'

// 示例图像数据（实际项目中应该从API获取）
const sampleImages: ImageInfoType[] = [
  {
    id: '1',
    name: '超声图像_001.jpg',
    url: ultrasoundImage1,
    width: 1024,
    height: 768,
    size: 320000,
    format: 'jpeg',
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    name: '超声图像_002.jpg',
    url: ultrasoundImage2,
    width: 1024,
    height: 768,
    size: 327680,
    format: 'jpeg',
    createdAt: '2024-01-15T11:15:00Z'
  }
]

// 计算属性
const isLoading = computed(() => loadingState.value === 'loading')
const canZoomIn = computed(() => currentZoom.value < 10)
const canZoomOut = computed(() => currentZoom.value > 0.1)
const zoomPercentage = computed(() => Math.round(currentZoom.value * 100))

// 加载默认图像
const loadDefaultImage = () => {
  if (sampleImages.length > 0) {
    currentImageInfo.value = sampleImages[0]
  }
}

// 处理工具栏操作
const handleToolbarAction = (action: ToolbarAction) => {
  if (action === 'fullscreen') {
    // TODO: 实现全屏功能
    ElMessage.info('全屏功能开发中...')
    return
  }

  // 其他操作转发给ImageViewer
  if (imageViewerRef.value) {
    // 调用 ImageViewer 组件的方法
    switch (action) {
      case 'zoom-in':
        imageViewerRef.value.zoomIn()
        break
      case 'zoom-out':
        imageViewerRef.value.zoomOut()
        break
      case 'zoom-fit':
        imageViewerRef.value.fitToCanvas()
        break
      case 'zoom-actual':
        imageViewerRef.value.actualSize()
        break
      case 'rotate-left':
        imageViewerRef.value.rotateLeft()
        break
      case 'rotate-right':
        imageViewerRef.value.rotateRight()
        break
      case 'reset':
        imageViewerRef.value.resetView()
        break
      default:
        console.warn('未处理的工具栏操作:', action)
    }
  }
}

// 处理图像加载成功
const handleImageLoad = (imageInfo: ImageInfoType) => {
  loadingState.value = 'loaded'
  ElMessage.success('图像加载成功')
}

// 处理图像加载失败
const handleImageError = (error: Error) => {
  loadingState.value = 'error'
  ElMessage.error(`图像加载失败: ${error.message}`)
}

// 处理缩放变化
const handleZoomChange = (zoom: number) => {
  currentZoom.value = zoom
}

// 切换图像
const switchImage = (imageInfo: ImageInfoType) => {
  loadingState.value = 'loading'
  currentImageInfo.value = imageInfo
}

// 组件挂载时加载默认图像
onMounted(() => {
  loadDefaultImage()
})
</script>

<template>
  <div class="ultrasound-image-viewer">
    <!-- 工具栏 -->
    <ImageToolbar
      :zoom-percentage="zoomPercentage"
      :can-zoom-in="canZoomIn"
      :can-zoom-out="canZoomOut"
      :loading="isLoading"
      @action="handleToolbarAction"
    />

    <!-- 主要内容区域 -->
    <div class="viewer-content">
      <!-- 图像查看器 -->
      <div class="viewer-main">
        <ImageViewer
          ref="imageViewerRef"
          :image-info="currentImageInfo"
          @load="handleImageLoad"
          @error="handleImageError"
          @zoom-change="handleZoomChange"
        />
      </div>

      <!-- 侧边栏 -->
      <div class="viewer-sidebar">
        <!-- 图像信息 -->
        <div class="info-section">
          <h4 class="section-title">图像信息</h4>
          <ImageInfo
            :image-info="currentImageInfo"
            :loading="isLoading"
          />
        </div>

        <!-- 图像列表 -->
        <div class="images-section">
          <h4 class="section-title">图像列表</h4>
          <div class="images-list">
            <div
              v-for="image in sampleImages"
              :key="image.id"
              class="image-item"
              :class="{ active: currentImageInfo?.id === image.id }"
              @click="switchImage(image)"
            >
              <div class="image-thumbnail">
                <img :src="image.url" :alt="image.name" />
              </div>
              <div class="image-name">{{ image.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ultrasound-image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* 允许收缩 */
  background: var(--el-bg-color);

  .viewer-content {
    display: flex;
    flex: 1;
    gap: 16px;
    padding: 16px;
    overflow: hidden;
    min-height: 0; /* 允许收缩 */

    .viewer-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      min-height: 0; /* 允许收缩 */
    }

    .viewer-sidebar {
      width: 280px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      flex-shrink: 0;

      .section-title {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }

      .info-section {
        flex-shrink: 0;
      }

      .images-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;

        .images-list {
          flex: 1;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .image-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border: 1px solid var(--el-border-color-light);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              border-color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
            }

            &.active {
              border-color: var(--el-color-primary);
              background: var(--el-color-primary-light-8);
            }

            .image-thumbnail {
              width: 48px;
              height: 36px;
              border-radius: 4px;
              overflow: hidden;
              flex-shrink: 0;
              background: var(--el-fill-color-light);

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .image-name {
              flex: 1;
              font-size: 13px;
              color: var(--el-text-color-primary);
              word-break: break-all;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ultrasound-image-viewer .viewer-content {
    .viewer-sidebar {
      width: 240px;
    }
  }
}

@media (max-width: 768px) {
  .ultrasound-image-viewer .viewer-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px;

    .viewer-sidebar {
      width: 100%;
      flex-direction: row;
      gap: 12px;

      .info-section,
      .images-section {
        flex: 1;
      }

      .images-section .images-list {
        max-height: 200px;
      }
    }
  }
}
</style>
