/**
 * 图像缩放相关逻辑
 */

import { ref, computed, readonly } from 'vue'
import type { ZoomConfig } from '../types'

export function useImageZoom(config: ZoomConfig = { min: 0.1, max: 5, step: 0.2, default: 1 }) {
  const currentZoom = ref(config.default)

  // 计算缩放百分比
  const zoomPercentage = computed(() => Math.round(currentZoom.value * 100))

  // 是否可以放大
  const canZoomIn = computed(() => currentZoom.value < config.max)

  // 是否可以缩小
  const canZoomOut = computed(() => currentZoom.value > config.min)

  // 放大
  const zoomIn = () => {
    if (canZoomIn.value) {
      const newZoom = Math.min(currentZoom.value + config.step, config.max)
      setZoom(newZoom)
    }
  }

  // 缩小
  const zoomOut = () => {
    if (canZoomOut.value) {
      const newZoom = Math.max(currentZoom.value - config.step, config.min)
      setZoom(newZoom)
    }
  }

  // 设置缩放级别
  const setZoom = (zoom: number) => {
    const clampedZoom = Math.max(config.min, Math.min(zoom, config.max))
    currentZoom.value = clampedZoom
  }

  // 重置缩放
  const resetZoom = () => {
    setZoom(config.default)
  }

  // 实际大小（100%）
  const actualSize = () => {
    setZoom(1)
  }

  // 根据鼠标滚轮缩放
  const handleWheelZoom = (event: WheelEvent, centerX?: number, centerY?: number) => {
    event.preventDefault()
    
    const delta = event.deltaY > 0 ? -config.step : config.step
    const newZoom = currentZoom.value + delta
    
    if (newZoom >= config.min && newZoom <= config.max) {
      setZoom(newZoom)
    }
  }

  // 获取预设缩放级别
  const getPresetZooms = () => {
    const presets = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4, 5]
    return presets.filter(zoom => zoom >= config.min && zoom <= config.max)
  }

  // 跳转到指定缩放级别
  const jumpToZoom = (zoom: number) => {
    if (zoom >= config.min && zoom <= config.max) {
      setZoom(zoom)
    }
  }

  return {
    currentZoom: readonly(currentZoom),
    zoomPercentage: readonly(zoomPercentage),
    canZoomIn: readonly(canZoomIn),
    canZoomOut: readonly(canZoomOut),
    zoomIn,
    zoomOut,
    setZoom,
    resetZoom,
    actualSize,
    handleWheelZoom,
    getPresetZooms,
    jumpToZoom
  }
}
