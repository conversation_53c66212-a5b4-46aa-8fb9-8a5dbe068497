/**
 * Fabric.js Canvas 相关逻辑
 */

import { ref, onUnmounted, nextTick, readonly } from 'vue'
import * as fabric from 'fabric'
import type { FabricCanvas, ImageInfo, ViewState } from '../types'

export function useFabricCanvas(canvasId: string) {
  const canvas = ref<FabricCanvas | null>(null)
  const currentImage = ref<fabric.Image | null>(null)
  const isReady = ref(false)

  // 网格配置
  const gridConfig = {
    size: 8, // 网格大小（像素）
    color: '#e8e8e8', // 网格线颜色
    backgroundColor: '#f8f9fa', // 背景颜色
    strokeWidth: 0.5 // 网格线宽度
  }

  // 创建网格背景
  const createGridBackground = () => {
    const { size, color, backgroundColor, strokeWidth } = gridConfig

    // 创建更精细的 SVG 网格图案
    const svgGrid = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" fill="${backgroundColor}"/>
        <path d="M ${size} 0 L 0 0 0 ${size}" fill="none" stroke="${color}" stroke-width="${strokeWidth}" opacity="0.6"/>
      </svg>
    `

    // 将 SVG 转换为 Data URL
    const svgDataUrl = `data:image/svg+xml;base64,${btoa(svgGrid)}`

    return svgDataUrl
  }

  // 设置网格背景
  const setGridBackground = async () => {
    if (!canvas.value) return

    const gridPattern = createGridBackground()

    try {
      // 创建图像元素
      const img = new Image()
      img.onload = () => {
        if (!canvas.value) return

        // 使用 Fabric.js 的 Pattern 创建背景
        const pattern = new fabric.Pattern({
          source: img,
          repeat: 'repeat'
        })

        canvas.value.backgroundColor = pattern
        canvas.value.renderAll()
      }
      img.onerror = () => {
        console.warn('Failed to load grid pattern, using solid color')
        if (canvas.value) {
          canvas.value.backgroundColor = '#f8f9fa'
          canvas.value.renderAll()
        }
      }
      img.src = gridPattern
    } catch (error) {
      console.warn('Failed to set grid background, using solid color:', error)
      canvas.value.backgroundColor = '#f8f9fa'
      canvas.value.renderAll()
    }
  }

  // 初始化Canvas
  const initCanvas = async () => {
    await nextTick()

    const canvasElement = document.getElementById(canvasId) as HTMLCanvasElement
    if (!canvasElement) {
      console.error(`Canvas element with id "${canvasId}" not found`)
      return
    }

    canvas.value = new fabric.Canvas(canvasElement, {
      selection: false,
      preserveObjectStacking: true,
      renderOnAddRemove: false,
      skipTargetFind: true,
      evented: true,
      backgroundColor: '#f8f9fa'
    })

    // 设置网格背景
    await setGridBackground()

    // 禁用对象选择和移动
    canvas.value.on('selection:created', () => {
      canvas.value?.discardActiveObject()
    })

    isReady.value = true
    console.log('✅ Fabric Canvas initialized')
  }

  // 加载图像
  const loadImage = (imageInfo: ImageInfo): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!canvas.value) {
        reject(new Error('Canvas not initialized'))
        return
      }

      fabric.FabricImage.fromURL(imageInfo.url, {
        crossOrigin: 'anonymous'
      }).then((img) => {
        if (!canvas.value) {
          reject(new Error('Canvas not available'))
          return
        }

        // 清除之前的图像
        canvas.value.clear()

        // 重新设置网格背景（clear会清除背景）
        setGridBackground()

        // 设置图像属性
        img.set({
          selectable: false,
          evented: false,
          hoverCursor: 'grab',
          moveCursor: 'grabbing'
        })

        // 居中显示图像
        const canvasWidth = canvas.value.getWidth()
        const canvasHeight = canvas.value.getHeight()

        img.set({
          left: canvasWidth / 2,
          top: canvasHeight / 2,
          originX: 'center',
          originY: 'center'
        })

        // 添加到画布
        canvas.value.add(img)
        currentImage.value = img

        // 适应画布大小
        fitToCanvas()

        canvas.value.renderAll()
        resolve()
      }).catch((error) => {
        console.error('图像加载失败:', error)
        reject(error)
      })
    })
  }

  // 适应画布大小
  const fitToCanvas = () => {
    if (!canvas.value || !currentImage.value) return

    const canvasWidth = canvas.value.getWidth()
    const canvasHeight = canvas.value.getHeight()
    const imgWidth = currentImage.value.getScaledWidth()
    const imgHeight = currentImage.value.getScaledHeight()

    const scaleX = canvasWidth / imgWidth
    const scaleY = canvasHeight / imgHeight
    const scale = Math.min(scaleX, scaleY) * 0.9 // 留一些边距

    currentImage.value.scale(scale)
    canvas.value.setZoom(1)
    canvas.value.renderAll()
  }

  // 设置缩放
  const setZoom = (zoom: number) => {
    if (!canvas.value) return
    
    canvas.value.setZoom(zoom)
    canvas.value.renderAll()
  }

  // 获取当前缩放
  const getZoom = (): number => {
    return canvas.value?.getZoom() || 1
  }

  // 重置视图
  const resetView = () => {
    if (!canvas.value || !currentImage.value) return

    currentImage.value.set({
      left: canvas.value.getWidth() / 2,
      top: canvas.value.getHeight() / 2,
      angle: 0
    })
    
    fitToCanvas()
  }

  // 旋转图像
  const rotateImage = (angle: number) => {
    if (!currentImage.value) return
    
    const currentAngle = currentImage.value.angle || 0
    currentImage.value.rotate(currentAngle + angle)
    canvas.value?.renderAll()
  }

  // 获取视图状态
  const getViewState = (): ViewState => {
    if (!canvas.value || !currentImage.value) {
      return { zoom: 1, panX: 0, panY: 0, rotation: 0 }
    }

    return {
      zoom: canvas.value.getZoom(),
      panX: currentImage.value.left || 0,
      panY: currentImage.value.top || 0,
      rotation: currentImage.value.angle || 0
    }
  }

  // 调整画布大小
  const resizeCanvas = (width: number, height: number) => {
    if (!canvas.value) return

    canvas.value.setDimensions({ width, height })
    // 重新设置网格背景以适应新尺寸
    setGridBackground()
    canvas.value.renderAll()
  }

  // 销毁Canvas
  const destroyCanvas = () => {
    if (canvas.value) {
      canvas.value.dispose()
      canvas.value = null
      currentImage.value = null
      isReady.value = false
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    destroyCanvas()
  })

  return {
    canvas: readonly(canvas),
    currentImage: readonly(currentImage),
    isReady: readonly(isReady),
    initCanvas,
    loadImage,
    fitToCanvas,
    setZoom,
    getZoom,
    resetView,
    rotateImage,
    getViewState,
    resizeCanvas,
    setGridBackground,
    destroyCanvas
  }
}
